import React, { useEffect, useState, useRef, useCallback } from 'react';
import { observer } from 'mobx-react';
import styles from './styles.module.scss';
import { Card, Toast } from 'antd-mobile';
import ClientRouter from '@/base/client/client-router';
import { useSearchParams } from 'react-router-dom';
import { superviseService } from '@/services/supervise';
import { to } from '@/utils/to';
import { gruops } from './mock';
import _ from 'lodash';
import SwiperImages, { ImageItem, SwiperImagesRef } from '@/components/swiper-images';
import { OperateType } from '@/views/security/enum';
import BrowserSVG from '@/assets/browser.svg';
import { useLoading } from '@/hooks/loading';
import { useMemoizedFn } from 'ahooks';
import dayjs from 'dayjs';
import SuperEmpty from '@/components/super-empty';
import HeaderNavbar from '@/components/header-navbar';
import { AiOutlineMenu } from 'react-icons/ai';
import SuperPopup from '@/components/super-popup';
import CardItem from '@/components/card-item';
import { getTimeDifference } from '@/utils/time';

const LogsDetail: React.FC = () => {
  const [searchParams] = useSearchParams();
  const clientRouter = ClientRouter.getRouter();
  const session_id = searchParams.get('session_id');
  const machine_string = searchParams.get('machine_string');
  const { loading, startLoading, stopLoading } = useLoading();
  const [infoVisible, setInfoVisible] = useState(false);
  const [logGroups, setLogGroups] = useState<SuperviseModule.LogsDetailGroups[]>(
    gruops as SuperviseModule.LogsDetailGroups[]
  );
  const swiperRef = useRef<SwiperImagesRef>(null);
  const [originData, setOriginData] = useState<SuperviseModule.LogsDetailResponse>();
  const [images, setImages] = useState<ImageItem[]>([]);
  const formatTime = useMemoizedFn((val) => {
    return dayjs(val * 1000).format('YYYY-MM-DD HH:mm:ss');
  });
  const getLogDetail = async () => {
    if (session_id) {
      const [err, res] = await to<SuperviseModule.LogsDetailResponse>(
        superviseService.superviseLogDetail({ session_id })
      );
      stopLoading();
      if (err) return;
      console.log('@@@LogDetail-data', res);
      setOriginData(res);
      const imgs: ImageItem[] = _.flatMap(res.groups, (group) =>
        group.events.map((item) => {
          const img = Number(item.type) === OperateType.Access ? BrowserSVG : item.image_url;
          return {
            src: img,
            alt: item.title,
          };
        })
      );
      setImages(imgs);
      console.log('@@@@imgs', imgs);
    }
  };
  useEffect(() => {
    startLoading();
    getLogDetail();
  }, []);

  console.log('@@@@logGroups', logGroups);
  return (
    <div className={styles.logsDetail}>
      <HeaderNavbar
        onBack={() => clientRouter.goBack()}
        title="日志详情"
        rightNode={<AiOutlineMenu size={24} onClick={() => setInfoVisible(true)} />}
      />
      {images.length === 0 ? (
        <SuperEmpty />
      ) : (
        <SwiperImages
          ref={swiperRef}
          images={images}
          height="250px"
          autoplay={true}
          showToolbar={true}
          showRotateButton={true}
          showScaleButtons={true}
          showFullscreenButton={true}
          showLandscapeButton={true}
          showViewerPlayButton={true}
          viewerAutoplayInterval={2000}
          viewerAutoplayLoop={true}
          pauseViewerOnInteraction={true}
          className={styles.customSwiper}
        />
      )}
      <SuperPopup title="基础信息" visible={infoVisible} onClose={() => setInfoVisible(false)}>
        <div className={styles.info}>
          <CardItem
            contentAlign="left"
            label="监管成员"
            content={`${originData?.user_username}(${originData?.user_name})`}
          />
          <CardItem
            contentAlign="left"
            label="平台账号"
            content={`${originData?.user_username}(${originData?.account_name})`}
          />
          <CardItem contentAlign="left" label="终端识别码" content={machine_string} />
          <CardItem
            contentAlign="left"
            label="记录时间"
            content={`${formatTime(originData?.start_time)} - ${formatTime(originData?.end_time)}`}
          />
          <CardItem
            contentAlign="left"
            label="总时长"
            content={getTimeDifference(originData?.start_time, originData?.end_time)}
          />
        </div>
      </SuperPopup>
    </div>
  );
};

export default observer(LogsDetail);
