import React, {
  useState,
  useEffect,
  useRef,
  useCallback,
  forwardRef,
  useImperativeHandle,
} from 'react';
import { Swiper } from 'antd-mobile';
import { PhotoProvider, PhotoView } from 'react-photo-view';
import { observer } from 'mobx-react';
import { isFullscreenSupported, toggleFullscreen } from './tools';
import 'react-photo-view/dist/react-photo-view.css';
import styles from './styles.module.scss';
import { ImageItem, SwiperImagesProps, SwiperImagesRef } from './types';
import {
  useViewerAutoplay,
  useInteractionDetection,
  usePerformanceMonitor,
} from './hooks/useViewerAutoplay';

import BrowserSVG from '@/assets/browser.svg';
import { AiOutlinePause } from 'react-icons/ai';
import { PlayOutline } from 'antd-mobile-icons';

// 重新导出类型以便外部使用
export type { ImageItem, SwiperImagesProps, SwiperImagesRef };

const SwiperImages = forwardRef<SwiperImagesRef, SwiperImagesProps>(
  (
    {
      images = [],
      autoplay = true,
      autoplayInterval = 2000,
      showIndicator = true,
      loop = true,
      height = '200px',
      pauseOnView = true,
      resumeOnExit = true,
      className = '',
      fallbackSrc = '',
      showToolbar = true,
      showRotateButton = true,
      showScaleButtons = true,
      showFullscreenButton = true,
      showLandscapeButton = true,
      showViewerPlayButton = true,
      viewerAutoplayInterval = 5000,
      viewerAutoplayLoop = true,
      pauseViewerOnInteraction = true,
      onChange,
      onImageClick,
    },
    ref
  ) => {
    const [currentIndex, setCurrentIndex] = useState(0);
    const [isAutoPlaying, setIsAutoPlaying] = useState(autoplay);
    const [isPhotoViewOpen, setIsPhotoViewOpen] = useState(false);
    const autoplayTimerRef = useRef<NodeJS.Timeout | null>(null);
    const swiperRef = useRef<any>(null);

    // 使用自定义 Hook 管理查看模式播放
    const viewerAutoplay = useViewerAutoplay({
      imageCount: images.length,
      interval: viewerAutoplayInterval,
      loop: viewerAutoplayLoop,
      pauseOnInteraction: pauseViewerOnInteraction,
      isViewerOpen: isPhotoViewOpen,
    });

    // 使用性能监控 Hook (开发环境)
    if (process.env.NODE_ENV === 'development') {
      usePerformanceMonitor(true);
    }

    // 清除自动播放定时器
    const clearAutoplayTimer = useCallback(() => {
      if (autoplayTimerRef.current) {
        clearInterval(autoplayTimerRef.current);
        autoplayTimerRef.current = null;
      }
    }, []);

    // 开始自动播放
    const startAutoplay = useCallback(() => {
      if (!autoplay || images.length <= 1) return;

      clearAutoplayTimer();
      autoplayTimerRef.current = setInterval(() => {
        setCurrentIndex((prevIndex) => {
          const nextIndex = loop
            ? (prevIndex + 1) % images.length
            : Math.min(prevIndex + 1, images.length - 1);
          if (swiperRef.current) {
            swiperRef.current.swipeTo(nextIndex);
          }
          return nextIndex;
        });
      }, autoplayInterval);
    }, [autoplay, autoplayInterval, images.length, loop, clearAutoplayTimer]);

    // 停止自动播放
    const stopAutoplay = useCallback(() => {
      setIsAutoPlaying(false);
      clearAutoplayTimer();
    }, [clearAutoplayTimer]);

    // 恢复自动播放
    const resumeAutoplay = useCallback(() => {
      if (autoplay) {
        setIsAutoPlaying(true);
        startAutoplay();
      }
    }, [autoplay, startAutoplay]);

    // 使用交互检测 Hook
    useInteractionDetection(
      pauseViewerOnInteraction && viewerAutoplay.isPlaying,
      isPhotoViewOpen,
      viewerAutoplay.stopPlay
    );

    // 处理轮播切换
    const handleSwiperChange = useCallback(
      (index: number) => {
        setCurrentIndex(index);
        onChange?.(index);
      },
      [onChange]
    );

    // 处理图片点击
    const handleImageClick = useCallback(
      (index: number) => {
        const image = images[index];
        onImageClick?.(index, image);

        if (pauseOnView) {
          stopAutoplay();
        }

        setIsPhotoViewOpen(true);
      },
      [images, onImageClick, pauseOnView, stopAutoplay]
    );

    // 处理PhotoView关闭
    const handlePhotoViewClose = useCallback(() => {
      setIsPhotoViewOpen(false);

      if (resumeOnExit && pauseOnView) {
        setTimeout(() => {
          resumeAutoplay();
        }, 100);
      }
    }, [resumeOnExit, pauseOnView, resumeAutoplay]);

    // 处理图片加载错误
    const handleImageError = useCallback(
      (e: React.SyntheticEvent<HTMLImageElement>) => {
        if (fallbackSrc) {
          e.currentTarget.src = fallbackSrc;
        }
      },
      [fallbackSrc]
    );

    // 初始化和清理自动播放
    useEffect(() => {
      if (isAutoPlaying && !isPhotoViewOpen) {
        startAutoplay();
      } else {
        clearAutoplayTimer();
      }

      return () => {
        clearAutoplayTimer();
      };
    }, [isAutoPlaying, isPhotoViewOpen, startAutoplay, clearAutoplayTimer]);

    // 当图片数组变化时重置状态
    useEffect(() => {
      setCurrentIndex(0);
      if (swiperRef.current) {
        swiperRef.current.swipeTo(0);
      }
    }, [images]);

    // 暴露组件方法给父组件
    useImperativeHandle(
      ref,
      () => ({
        swipeTo: (index: number) => {
          if (swiperRef.current && index >= 0 && index < images.length) {
            swiperRef.current.swipeTo(index);
            setCurrentIndex(index);
          }
        },
        startAutoplay: resumeAutoplay,
        stopAutoplay,
        getCurrentIndex: () => currentIndex,
      }),
      [currentIndex, images.length, resumeAutoplay, stopAutoplay]
    );

    if (!images || images.length === 0) {
      return (
        <div className={`${styles.swiperContainer} ${className}`} style={{ height }}>
          <div className={styles.emptyState}>暂无图片</div>
        </div>
      );
    }

    return (
      <div className={`${styles.swiperContainer} ${className}`}>
        <PhotoProvider
          onVisibleChange={(visible) => {
            if (!visible) {
              handlePhotoViewClose();
            }
          }}
          onIndexChange={(newIndex) => {
            // 使用 Hook 处理索引变化
            viewerAutoplay.handleIndexChange(newIndex);
          }}
          toolbarRender={
            showToolbar
              ? ({ rotate, onRotate, scale, onScale, index, onIndexChange, images }) => {
                  const buttons: React.ReactElement[] = [];

                  // 查看模式播放按钮
                  if (showViewerPlayButton && images.length > 1) {
                    if (process.env.NODE_ENV === 'development') {
                      console.log('渲染播放按钮，当前播放状态:', viewerAutoplay.isPlaying);
                    }

                    buttons.push(
                      <button
                        key="viewer-play"
                        className={styles.toolbarButton}
                        onClick={() => {
                          if (process.env.NODE_ENV === 'development') {
                            console.log('播放按钮被点击，当前状态:', viewerAutoplay.isPlaying);
                          }
                          viewerAutoplay.togglePlay(index, onIndexChange);
                        }}
                        title={viewerAutoplay.isPlaying ? '暂停播放' : '开始播放'}
                        aria-label={viewerAutoplay.isPlaying ? '暂停播放' : '开始播放'}
                      >
                        {viewerAutoplay.isPlaying ? (
                          <svg
                            width="20"
                            height="20"
                            viewBox="0 0 24 24"
                            fill="currentColor"
                            aria-hidden="true"
                          >
                            <path d="M6 19h4V5H6v14zm8-14v14h4V5h-4z" />
                          </svg>
                        ) : (
                          <svg
                            width="20"
                            height="20"
                            viewBox="0 0 24 24"
                            fill="currentColor"
                            aria-hidden="true"
                          >
                            <path d="M8 5v14l11-7z" />
                          </svg>
                        )}
                      </button>
                    );
                  }

                  // 旋转按钮
                  if (showRotateButton) {
                    buttons.push(
                      <button
                        key="rotate"
                        className={styles.toolbarButton}
                        onClick={() => onRotate(rotate + 90)}
                        title="旋转"
                      >
                        <svg width="20" height="20" viewBox="0 0 24 24" fill="currentColor">
                          <path d="M12 6v3l4-4-4-4v3c-4.42 0-8 3.58-8 8 0 1.57.46 3.03 1.24 4.26L6.7 14.8c-.45-.83-.7-1.79-.7-2.8 0-3.31 2.69-6 6-6zm6.76 1.74L17.3 9.2c.44.84.7 1.79.7 2.8 0 3.31-2.69 6-6 6v-3l-4 4 4 4v-3c4.42 0 8-3.58 8-8 0-1.57-.46-3.03-1.24-4.26z" />
                        </svg>
                      </button>
                    );
                  }

                  // 缩放按钮
                  if (showScaleButtons) {
                    buttons.push(
                      <button
                        key="zoom-in"
                        className={styles.toolbarButton}
                        onClick={() => onScale(scale + 0.5)}
                        title="放大"
                      >
                        <svg width="20" height="20" viewBox="0 0 24 24" fill="currentColor">
                          <path d="M15.5 14h-.79l-.28-.27C15.41 12.59 16 11.11 16 9.5 16 5.91 13.09 3 9.5 3S3 5.91 3 9.5 5.91 16 9.5 16c1.61 0 3.09-.59 4.23-1.57l.27.28v.79l5 4.99L20.49 19l-4.99-5zm-6 0C7.01 14 5 11.99 5 9.5S7.01 5 9.5 5 14 7.01 14 9.5 11.99 14 9.5 14z" />
                          <path d="M12 10h-2v2H9v-2H7V9h2V7h1v2h2v1z" />
                        </svg>
                      </button>,
                      <button
                        key="zoom-out"
                        className={styles.toolbarButton}
                        onClick={() => onScale(Math.max(0.5, scale - 0.5))}
                        title="缩小"
                      >
                        <svg width="20" height="20" viewBox="0 0 24 24" fill="currentColor">
                          <path d="M15.5 14h-.79l-.28-.27C15.41 12.59 16 11.11 16 9.5 16 5.91 13.09 3 9.5 3S3 5.91 3 9.5 5.91 16 9.5 16c1.61 0 3.09-.59 4.23-1.57l.27.28v.79l5 4.99L20.49 19l-4.99-5zm-6 0C7.01 14 5 11.99 5 9.5S7.01 5 9.5 5 14 7.01 14 9.5 11.99 14 9.5 14z" />
                          <path d="M7 9v1h5V9H7z" />
                        </svg>
                      </button>
                    );
                  }

                  // 全屏按钮
                  if (showFullscreenButton) {
                    buttons.push(
                      <button
                        key="fullscreen"
                        className={styles.toolbarButton}
                        onClick={async () => {
                          try {
                            if (!isFullscreenSupported()) {
                              console.warn('当前浏览器不支持全屏功能');
                              return;
                            }
                            await toggleFullscreen();
                          } catch (error) {
                            console.warn('全屏操作失败:', error);
                          }
                        }}
                        title={isFullscreenSupported() ? '全屏' : '全屏功能不可用'}
                        disabled={!isFullscreenSupported()}
                      >
                        <svg width="20" height="20" viewBox="0 0 24 24" fill="currentColor">
                          <path d="M7 14H5v5h5v-2H7v-3zm-2-4h2V7h3V5H5v5zm12 7h-3v2h5v-5h-2v3zM14 5v2h3v3h2V5h-5z" />
                        </svg>
                      </button>
                    );
                  }
                  return buttons.length > 0 ? (
                    <div className={styles.customToolbar}>{buttons}</div>
                  ) : null;
                }
              : undefined
          }
        >
          <Swiper
            ref={swiperRef}
            loop={loop}
            indicator={showIndicator ? undefined : false}
            onIndexChange={handleSwiperChange}
            style={{ height }}
            className={styles.swiper}
          >
            {images.map((image, index) => (
              <Swiper.Item key={`${image.src}-${index}`}>
                <PhotoView src={image.src}>
                  <div className={styles.imageWrapper} onClick={() => handleImageClick(index)}>
                    <img
                      src={image.thumbnail || image.src}
                      alt={image.alt || `图片 ${index + 1}`}
                      className={styles.image}
                      onError={handleImageError}
                      loading="lazy"
                    />
                  </div>
                </PhotoView>
              </Swiper.Item>
            ))}
          </Swiper>
        </PhotoProvider>

        {/* 自定义控制按钮 */}
        {autoplay && images.length > 1 && (
          <div className={styles.controls}>
            <div
              className={`${styles.controlButton}`}
              onClick={isAutoPlaying ? stopAutoplay : resumeAutoplay}
              aria-label={isAutoPlaying ? '暂停自动播放' : '开始自动播放'}
            >
              {isAutoPlaying ? <AiOutlinePause size={24} /> : <PlayOutline fontSize={24} />}
            </div>
          </div>
        )}
      </div>
    );
  }
);

export default observer(SwiperImages);
