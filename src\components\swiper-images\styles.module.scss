.swiperContainer {
  position: relative;
  width: 100%;
  overflow: hidden;
  border-radius: 8px;
  background-color: #f5f5f5;

  .swiper {
    width: 100%;
    height: 100%;

    .imageWrapper {
      width: 100%;
      height: 100%;
      display: flex;
      align-items: center;
      justify-content: center;
      cursor: pointer;
      position: relative;
      overflow: hidden;

      &:active {
        transform: scale(0.98);
        transition: transform 0.1s ease;
      }

      .image {
        width: 100%;
        height: 100%;
        object-fit: cover;
        display: block;
        transition: transform 0.3s ease;

        &:hover {
          transform: scale(1.02);
        }
      }
    }
  }

  .emptyState {
    display: flex;
    align-items: center;
    justify-content: center;
    height: 100%;
    color: #999;
    font-size: 14px;
    background-color: #f8f8f8;
  }

  .controls {
    position: absolute;
    left: 12px;
    bottom: 12px;
    z-index: 10;

    .controlButton {
      font-size: $icon-font-size;
      width: 36px;
      height: 36px;
      border: none;
      border-radius: 50%;
      background-color: rgba(0, 0, 0, 0.4);
      color: white;
      display: flex;
      align-items: center;
      justify-content: center;
      cursor: pointer;
      font-size: 14px;
      transition: all 0.2s ease;
      backdrop-filter: blur(4px);

      &:hover {
        background-color: rgba(0, 0, 0, 0.8);
        transform: scale(1.1);
      }

      &:active {
        transform: scale(0.95);
      }
    }
  }
}

// 移动端适配
@media (max-width: 768px) {
  .swiperContainer {
    .controls {
      left: 8px;
      bottom: 8px;

      .controlButton {
        width: 32px;
        height: 32px;
        font-size: 12px;
      }
    }

    .swiper {
      .imageWrapper {
        &:active {
          transform: none;
        }

        .image {
          &:hover {
            transform: none;
          }
        }
      }
    }
  }
}

// 横屏模式适配
@media (orientation: landscape) and (max-height: 500px) {
  .swiperContainer {
    .controls {
      left: 6px;
      bottom: 6px;

      .controlButton {
        width: 28px;
        height: 28px;
        font-size: 11px;
      }
    }
  }
}

// 高分辨率屏幕适配
@media (-webkit-min-device-pixel-ratio: 2),
(min-resolution: 192dpi) {
  .swiperContainer {
    .swiper {
      .imageWrapper {
        .image {
          image-rendering: -webkit-optimize-contrast;
          image-rendering: crisp-edges;
        }
      }
    }
  }
}

// 暗色主题适配
@media (prefers-color-scheme: dark) {
  .swiperContainer {
    background-color: #2a2a2a;

    .emptyState {
      background-color: #1a1a1a;
      color: #ccc;
    }
  }
}

// 减少动画效果（用户偏好）
@media (prefers-reduced-motion: reduce) {
  .swiperContainer {
    .swiper {
      .imageWrapper {
        .image {
          transition: none;

          &:hover {
            transform: none;
          }
        }

        &:active {
          transform: none;
          transition: none;
        }
      }
    }

    .controls {
      .controlButton {
        transition: none;

        &:hover {
          transform: none;
        }

        &:active {
          transform: none;
        }
      }
    }
  }
}

// 自定义工具栏样式
.customToolbar {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 12px;
  background-color: rgba(0, 0, 0, 0.7);
  border-radius: 6px;
  backdrop-filter: blur(8px);

  .toolbarButton {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 40px;
    height: 40px;
    border: none;
    border-radius: 4px;
    background-color: rgba(255, 255, 255, 0.1);
    color: white;
    cursor: pointer;
    transition: all 0.2s ease;

    &:hover {
      background-color: rgba(255, 255, 255, 0.2);
      transform: scale(1.05);
    }

    &:active {
      transform: scale(0.95);
    }

    &:disabled {
      opacity: 0.5;
      cursor: not-allowed;

      &:hover {
        background-color: rgba(255, 255, 255, 0.1);
        transform: none;
      }
    }

    svg {
      width: 20px;
      height: 20px;
    }

    // 播放按钮特殊样式
    &[title*="播放"] {
      background-color: rgba(76, 175, 80, 0.2);

      &:hover {
        background-color: rgba(76, 175, 80, 0.3);
      }
    }

    // 暂停按钮特殊样式
    &[title*="暂停"] {
      background-color: rgba(255, 152, 0, 0.2);

      &:hover {
        background-color: rgba(255, 152, 0, 0.3);
      }
    }

    // 横屏按钮特殊样式
    &[title*="横屏"],
    &[title*="竖屏"] {
      background-color: rgba(33, 150, 243, 0.2);

      &:hover {
        background-color: rgba(33, 150, 243, 0.3);
      }
    }
  }
}

// 移动端工具栏适配
@media (max-width: 768px) {
  .customToolbar {
    gap: 6px;
    padding: 6px 10px;

    .toolbarButton {
      width: 36px;
      height: 36px;

      svg {
        width: 18px;
        height: 18px;
      }
    }
  }
}

// 横屏模式工具栏适配
@media (orientation: landscape) and (max-height: 500px) {
  .customToolbar {
    gap: 4px;
    padding: 4px 8px;

    .toolbarButton {
      width: 32px;
      height: 32px;

      svg {
        width: 16px;
        height: 16px;
      }
    }
  }
}