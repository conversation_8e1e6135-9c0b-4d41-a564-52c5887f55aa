import { useCallback, useRef, useState, useEffect } from 'react';

export interface UseViewerAutoplayOptions {
  /** 图片数量 */
  imageCount: number;
  /** 播放间隔时间（毫秒） */
  interval: number;
  /** 是否循环播放 */
  loop: boolean;
  /** 是否启用用户交互暂停 */
  pauseOnInteraction: boolean;
  /** 是否在查看模式打开 */
  isViewerOpen: boolean;
}

export interface UseViewerAutoplayReturn {
  /** 是否正在播放 */
  isPlaying: boolean;
  /** 开始播放 */
  startPlay: (initialIndex: number, onIndexChange: (index: number) => void) => void;
  /** 停止播放 */
  stopPlay: () => void;
  /** 切换播放状态 */
  togglePlay: (currentIndex: number, onIndexChange: (index: number) => void) => void;
  /** 处理索引变化（用于检测用户交互） */
  handleIndexChange: (newIndex: number) => void;
  /** 清理资源 */
  cleanup: () => void;
}

/**
 * 查看模式自动播放 Hook
 * 提供完整的播放控制功能，包括用户交互检测和性能优化
 */
export const useViewerAutoplay = (options: UseViewerAutoplayOptions): UseViewerAutoplayReturn => {
  const {
    imageCount,
    interval,
    loop,
    pauseOnInteraction,
    isViewerOpen
  } = options;

  const [isPlaying, setIsPlaying] = useState(false);
  const timerRef = useRef<NodeJS.Timeout | null>(null);
  const currentIndexRef = useRef(0);
  const lastAutoSwitchTimeRef = useRef(0);
  const onIndexChangeRef = useRef<((index: number) => void) | null>(null);

  // 清理定时器
  const clearTimer = useCallback(() => {
    if (timerRef.current) {
      clearInterval(timerRef.current);
      timerRef.current = null;
    }
  }, []);

  // 开始播放
  const startPlay = useCallback((initialIndex: number, onIndexChange: (index: number) => void) => {
    if (imageCount <= 1) return;

    clearTimer();

    // 延迟状态更新，避免在渲染过程中更新状态
    setTimeout(() => {
      setIsPlaying(true);
    }, 0);

    currentIndexRef.current = initialIndex;
    onIndexChangeRef.current = onIndexChange;

    if (process.env.NODE_ENV === 'development') {
      console.log('开始查看模式播放，初始索引:', initialIndex);
    }

    timerRef.current = setInterval(() => {
      const currentIndex = currentIndexRef.current;
      let nextIndex = currentIndex + 1;

      if (nextIndex >= imageCount) {
        if (loop) {
          nextIndex = 0;
        } else {
          // 不循环时播放到最后停止
          if (process.env.NODE_ENV === 'development') {
            console.log('播放结束，停止播放');
          }
          clearTimer();

          // 延迟状态更新，避免在渲染过程中更新状态
          setTimeout(() => {
            setIsPlaying(false);
          }, 0);

          return;
        }
      }

      if (process.env.NODE_ENV === 'development') {
        console.log('自动切换到下一张:', nextIndex);
      }

      // 记录自动切换时间
      lastAutoSwitchTimeRef.current = Date.now();
      currentIndexRef.current = nextIndex;
      
      if (onIndexChangeRef.current) {
        onIndexChangeRef.current(nextIndex);
      }
    }, interval);
  }, [imageCount, interval, loop, clearTimer]);

  // 停止播放
  const stopPlay = useCallback(() => {
    if (process.env.NODE_ENV === 'development') {
      console.log('停止查看模式播放');
    }
    clearTimer();

    // 延迟状态更新，避免在渲染过程中更新状态
    setTimeout(() => {
      setIsPlaying(false);
    }, 0);

    onIndexChangeRef.current = null;
  }, [clearTimer]);

  // 切换播放状态
  const togglePlay = useCallback((currentIndex: number, onIndexChange: (index: number) => void) => {
    if (isPlaying) {
      stopPlay();
    } else {
      startPlay(currentIndex, onIndexChange);
    }
  }, [isPlaying, startPlay, stopPlay]);

  // 处理索引变化（检测用户交互）
  const handleIndexChange = useCallback((newIndex: number) => {
    currentIndexRef.current = newIndex;

    // 延迟检测用户交互，避免在渲染过程中更新状态
    setTimeout(() => {
      // 检测用户手动切换
      if (pauseOnInteraction && isPlaying && isViewerOpen) {
        const now = Date.now();
        const timeSinceLastAutoSwitch = now - lastAutoSwitchTimeRef.current;

        if (process.env.NODE_ENV === 'development') {
          console.log('索引变化检测:', {
            newIndex,
            timeSinceLastAutoSwitch,
            interval,
            isPlaying
          });
        }

        // 如果不是刚刚自动切换的（时间差大于100ms），则认为是用户手动切换
        if (timeSinceLastAutoSwitch > 100) {
          if (process.env.NODE_ENV === 'development') {
            console.log('检测到用户手动切换，暂停播放');
          }
          stopPlay();
        }
      }
    }, 0);
  }, [pauseOnInteraction, isPlaying, isViewerOpen, interval, stopPlay]);

  // 清理资源
  const cleanup = useCallback(() => {
    clearTimer();

    // 延迟状态更新，避免在渲染过程中更新状态
    setTimeout(() => {
      setIsPlaying(false);
    }, 0);

    onIndexChangeRef.current = null;
  }, [clearTimer]);

  // 查看模式关闭时自动停止播放
  useEffect(() => {
    if (!isViewerOpen && isPlaying) {
      stopPlay();
    }
  }, [isViewerOpen, isPlaying, stopPlay]);

  // 组件卸载时清理资源
  useEffect(() => {
    return cleanup;
  }, [cleanup]);

  return {
    isPlaying,
    startPlay,
    stopPlay,
    togglePlay,
    handleIndexChange,
    cleanup,
  };
};

/**
 * 用户交互检测 Hook
 * 监听用户在查看模式下的交互行为
 */
export const useInteractionDetection = (
  isEnabled: boolean,
  isViewerOpen: boolean,
  onInteraction: () => void
) => {
  useEffect(() => {
    if (!isEnabled || !isViewerOpen) {
      return;
    }

    const handleUserInteraction = (e: Event) => {
      // 检查事件是否来自工具栏按钮
      const target = e.target as HTMLElement;
      if (target && (
        target.closest('.PhotoView-Slider__toolbarIcon') ||
        target.closest('[class*="toolbarButton"]') ||
        target.closest('button')
      )) {
        return;
      }

      if (process.env.NODE_ENV === 'development') {
        console.log('检测到用户交互:', e.type);
      }
      onInteraction();
    };

    // 设置事件监听器
    const setupListeners = () => {
      const photoViewContainer = document.querySelector('.PhotoView-Portal') || 
                               document.querySelector('[class*="PhotoView"]');

      if (photoViewContainer) {
        const events = ['mousedown', 'touchstart', 'wheel', 'keydown'] as const;
        events.forEach(eventType => {
          photoViewContainer.addEventListener(eventType, handleUserInteraction, {
            capture: true,
            passive: true
          });
        });

        return () => {
          events.forEach(eventType => {
            photoViewContainer.removeEventListener(eventType, handleUserInteraction, true);
          });
        };
      }
      return () => {};
    };

    // 延迟设置监听器，确保 PhotoView 已渲染
    const timer = setTimeout(setupListeners, 100);

    return () => {
      clearTimeout(timer);
    };
  }, [isEnabled, isViewerOpen, onInteraction]);
};

/**
 * 性能监控 Hook
 * 监控组件的性能指标
 */
export const usePerformanceMonitor = (isEnabled: boolean = false) => {
  const [metrics, setMetrics] = useState<{
    renderTime: number;
    memoryUsage: number;
    lastUpdate: number;
  }>({
    renderTime: 0,
    memoryUsage: 0,
    lastUpdate: 0,
  });

  const startTimeRef = useRef<number>(0);

  const startMeasure = useCallback(() => {
    if (!isEnabled) return;
    startTimeRef.current = performance.now();
  }, [isEnabled]);

  const endMeasure = useCallback(() => {
    if (!isEnabled) return;
    
    const endTime = performance.now();
    const renderTime = endTime - startTimeRef.current;
    const memoryUsage = (performance as any).memory ? (performance as any).memory.usedJSHeapSize : 0;

    setMetrics({
      renderTime,
      memoryUsage,
      lastUpdate: Date.now(),
    });

    if (process.env.NODE_ENV === 'development') {
      console.log('性能指标:', {
        renderTime: `${renderTime.toFixed(2)}ms`,
        memoryUsage: `${(memoryUsage / 1024 / 1024).toFixed(2)}MB`,
      });
    }
  }, [isEnabled]);

  return {
    metrics,
    startMeasure,
    endMeasure,
  };
};
